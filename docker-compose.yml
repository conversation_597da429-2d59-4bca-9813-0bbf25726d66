version: '3.8'

services:
    web:
        build:
            context: .
            dockerfile: FrankenPHP.Alpine.Dockerfile
        ports:
            - "8000:8000"
        environment:
            - APP_NAME=Linkanization
            - NODE_ENV=production
            - APP_KEY=base64:a7JCXU7QvMpnGMxqpJxBfweOoyZvL25RgTv8kOdb6t0=
            - DB_CONNECTION=pgsql
            - DB_HOST=db
            - DB_PORT=5432
            - DB_DATABASE=linkanization
            - DB_USERNAME=postgres
            - DB_PASSWORD=postgres
        depends_on:
            - db
        volumes:
            - .:/app
            - /app/node_modules
        restart: unless-stopped

    db:
        image: postgres:17-alpine
        ports:
            - "5432:5432"
        environment:
            - POSTGRES_USER=postgres
            - POSTGRES_PASSWORD=postgres
            - POSTGRES_DB=linkanization
        volumes:
            - postgres_data:/var/lib/postgresql/data
        restart: unless-stopped

    queue-worker:
        build:
            context: .
            dockerfile: FrankenPHP.Alpine.Dockerfile
        environment:
            - APP_NAME=Linkanization
            - NODE_ENV=production
            - APP_KEY=base64:a7JCXU7QvMpnGMxqpJxBfweOoyZvL25RgTv8kOdb6t0=
            - DB_CONNECTION=pgsql
            - DB_HOST=db
            - DB_PORT=5432
            - DB_DATABASE=linkanization
            - DB_USERNAME=postgres
            - DB_PASSWORD=postgres
        depends_on:
            - db
        restart: unless-stopped
        command: php artisan queue:work

#    metabase:
#        image: metabase/metabase:latest
#        ports:
#            - "3000:3000"
#        environment:
#            - MB_DB_TYPE=postgres
#            - MB_DB_DBNAME=metabase
#            - MB_DB_PORT=5432
#            - MB_DB_USER=postgres
#            - MB_DB_PASS=postgres
#            - MB_DB_HOST=db
#        depends_on:
#            - db
#        restart: unless-stopped

#    pgadmin:
#        image: dpage/pgadmin4:latest
#        ports:
#            - "5050:5050"
#        environment:
#            - PGADMIN_DEFAULT_EMAIL=<EMAIL>
#            - PGADMIN_DEFAULT_PASSWORD=admin
#        volumes:
#            - pgadmin_data:/var/lib/pgadmin
#        depends_on:
#            - db
#        restart: unless-stopped

volumes:
    postgres_data:
#    pgadmin_data:
