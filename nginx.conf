server {
    listen 2000;  # Listen on port 2000 directly (host networking)
    server_name localhost;

    # Get the real client IP from the connection
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;

    location / {
        proxy_pass http://**********:8000;  # Direct connection to web container

        # Forward the REAL client IP (preserved with host networking)
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Additional headers for better IP detection
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Client-IP $remote_addr;
    }
}
